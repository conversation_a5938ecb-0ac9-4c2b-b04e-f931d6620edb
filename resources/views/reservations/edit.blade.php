@extends('layouts.admin')

@section('title', 'Edit Reservation - SMP Online')

@section('content')
    <!-- <PERSON> Header -->
    <div class="d-md-flex d-block align-items-center justify-content-between my-4 page-header-breadcrumb">
        <h1 class="page-title fw-semibold fs-18 mb-0">Reservation Management</h1>
        <div class="ms-md-1 ms-0">
            <nav>
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.index') }}">Reservations</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('reservations.show', $reservation) }}">Reservation
                            #{{ $reservation->id }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Edit</li>
                </ol>
            </nav>
        </div>
    </div>
    <!-- <PERSON> Header Close -->

    <!-- Success/Error Messages -->
    @if (session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="ti ti-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="ti ti-exclamation-triangle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- Edit Reservation Form -->
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <div class="card-header justify-content-between">
                    <div class="card-title">
                        <i class="ti ti-edit me-2"></i>Edit Reservation #{{ $reservation->id }}
                        <span
                            class="badge bg-{{ $reservation->status_color }}-transparent text-{{ $reservation->status_color }} ms-2">
                            {{ $reservation->status }}
                        </span>
                    </div>
                    <div class="d-flex gap-2">
                        <a href="{{ route('reservations.show', $reservation) }}" class="btn btn-primary btn-sm">
                            <i class="bx bx-calendar-event me-1"></i>View Details #{{ $reservation->id }}
                        </a>
                        <a href="{{ route('reservations.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-list me-1"></i>View List
                        </a>
                        <a href="{{ route('calendar.index') }}" class="btn btn-success btn-sm">
                            <i class="ti ti-calendar me-1"></i>View Calendar
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('reservations.update', $reservation) }}" id="reservationForm">
                        @csrf
                        @method('PUT')

                        <div class="row gy-4">
                            <!-- Left Column: Field Selection, Utilities, and Cost Overview -->
                            <div class="col-xl-6">
                                <!-- Field Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Field Selection
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Field Selection -->
                                            <div class="col-xl-12">
                                                <label for="field_id" class="form-label">Field <span
                                                        class="text-danger">*</span></label>
                                                <select name="field_id" id="field_id" required
                                                    onchange="updateFieldInfo(); loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();"
                                                    class="form-select @error('field_id') is-invalid @enderror">
                                                    <option value="">Select Field</option>
                                                    @foreach ($fields as $field)
                                                        <option value="{{ $field->id }}"
                                                            data-rate="{{ $field->hourly_rate }}"
                                                            data-rate2="{{ $field->night_hourly_rate }}"
                                                            data-capacity="{{ $field->capacity }}"
                                                            data-type="{{ $field->type }}"
                                                            data-opening="{{ $field->opening_time }}"
                                                            data-closing="{{ $field->closing_time }}"
                                                            data-min-hours="{{ $field->min_booking_hours }}"
                                                            data-max-hours="{{ $field->max_booking_hours }}"
                                                            data-anochi="{{ $field->night_time_start }}"
                                                            {{ old('field_id', $reservation->field_id) == $field->id ? 'selected' : '' }}>
                                                            {{ $field->name }}
                                                            <!--
                                                                                                                        XCG {{ number_format($field->hourly_rate, 2) }}/hr -->
                                                        </option>
                                                    @endforeach
                                                </select>
                                                @error('field_id')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Field Information Display -->
                                            <div class="col-xl-12">
                                                <div id="fieldInfo" class="alert alert-info">
                                                    <p class="mb-1"><i class="ti ti-info-circle me-2"></i><strong>Field Information</strong></p>
                                                    <p class="mb-1" style="margin-left: 1.5rem;"><strong>Capacity:</strong> <span
                                                            id="fieldCapacity">{{ $reservation->field->capacity }}</span>
                                                        people</p>
                                                    <p class="mb-1" style="margin-left: 1.5rem;"><strong>Rates:</strong> Day XCG <span
                                                            id="fieldRate">{{ number_format($reservation->field->hourly_rate, 2) }}</span>/hr, Night XCG <span
                                                            id="fieldRate2">{{ number_format($reservation->field->night_hourly_rate, 2) }}</span>/hr
                                                    </p>
                                                    <p class="mb-0" style="margin-left: 1.5rem;"><strong>Work Hours:</strong> <span
                                                            id="fieldHours">{{ $reservation->field->opening_time }} -
                                                            {{ $reservation->field->closing_time }}</span> | <strong>Duration:</strong> <span
                                                            id="fieldDuration">Min {{ $reservation->field->min_booking_hours }}
                                                            - Max {{ $reservation->field->max_booking_hours }}</span> hours</p>
                                                </div>
                                            </div>

                                            <!-- Date Selection -->
                                            <div class="col-xl-12">
                                                <x-custom-date-picker
                                                    name="booking_date"
                                                    id="booking_date"
                                                    :value="old('booking_date', $reservation->booking_date->format('Y-m-d'))"
                                                    label="Date"
                                                    :required="true"
                                                    :on-change="'loadAvailability(); checkUtilityPrerequisites(); enableProgressiveFields();'" />
                                            </div>

                                            <!-- Start Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="start_time" class="form-label">Start Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="start_time" id="start_time" required
                                                    onchange="updateEndTimeOptions();"
                                                    class="form-select @error('start_time') is-invalid @enderror">
                                                    <option value="">Select Start Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text" id="startTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a field first
                                                </div>
                                                @error('start_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- End Time Selection -->
                                            <div class="col-xl-6">
                                                <label for="end_time" class="form-label">End Time <span
                                                        class="text-danger">*</span></label>
                                                <select name="end_time" id="end_time" required
                                                    onchange="handleEndTimeChange();"
                                                    class="form-select @error('end_time') is-invalid @enderror">
                                                    <option value="">Select End Time</option>
                                                    <!-- Options will be populated by JavaScript -->
                                                </select>
                                                <div class="form-text" id="endTimeHelp">
                                                    <i class="ti ti-info-circle me-1"></i>Please select a start time first
                                                </div>
                                                @error('end_time')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Utility Selection Section -->
                                <div class="card custom-card shadow-none border mb-4">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Utility Selection (Optional)
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Utilities -->
                                            <div class="col-xl-12">
                                                <label class="form-label">Add Utility</label>

                                                <!-- Utility Selection Section -->
                                                <div id="utilitySelectionSection" class="utility-section-disabled">
                                                    <div class="row g-2 align-items-center mb-3">
                                                        <div class="col-md-5">
                                                            <select id="utilitySelect" class="form-select" disabled>
                                                                <option value="">Select Utility</option>
                                                                @foreach ($utilities as $utility)
                                                                    <option value="{{ $utility->id }}"
                                                                        data-name="{{ $utility->name }}"
                                                                        data-rate="{{ $utility->hourly_rate }}">
                                                                        {{ $utility->name }} -
                                                                        XCG {{ number_format($utility->hourly_rate, 2) }}
                                                                    </option>
                                                                @endforeach
                                                            </select>
                                                        </div>
                                                        <div class="col-md-3">
                                                            <input type="number" id="utilityQuantity" class="form-control"
                                                                min="1" step="1" value="1" placeholder="Quantity" disabled>
                                                        </div>
                                                        <div class="col-md-2">
                                                            <button type="button" class="btn btn-primary w-100"
                                                                onclick="addUtility()" id="addUtilityBtn" disabled>Add</button>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div id="utilityTableContainer" class="utility-table-enabled">
                                                    <div class="table-responsive">
                                                        <table class="table table-bordered text-nowrap" id="utilityTable">
                                                            <thead id="utilityTableHeader" class="d-none">
                                                                <tr>
                                                                    <th>Utility</th>
                                                                    <th>Quantity</th>
                                                                    <th>Rate</th>
                                                                    <th>Cost</th>
                                                                    <th>Action</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="utilityTableBody">
                                                                <!-- Existing utilities will be populated by JS -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>

                                                <!-- Utility Prerequisites Message -->
                                                <div id="utilityPrerequisitesMessage" class="form-text mb-1 d-none">
                                                    <p class="mb-0"><i class="ti ti-info-circle me-2"></i><strong>Complete reservation details first</strong></p>
                                                    <p class="mb-0" style="margin-left: 1.3rem;">Please select field, date, start time, and end time before adding utilities.</p>
                                                </div>
                                            </div>
                                            <!-- end Utilities -->
                                        </div>
                                    </div>
                                </div>

                                <!-- Total Cost Overview Section -->
                                <div class="card custom-card shadow-none border mb-4 d-none" id="costOverviewCard">
                                    <div class="card-header">
                                        <div class="card-title">
                                            <i class="me-2"></i>Total Cost Overview
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Availability Check -->
                                            <div class="col-xl-12">
                                                <div id="availabilityCheck" class="d-none">
                                                    <div id="availabilityMessage" class="alert"></div>
                                                </div>
                                            </div>
                                            <!------------------------------------------------------------------>
                                            <!-- Enhanced Cost Display with Detailed Breakdown -->
                                            <div class="col-xl-12">
                                                <div id="costDisplay" class="alert alert-success d-none">
                                                    <h6 class="fw-semibold">Reservation Cost Breakdown</h6>

                                                    <!-- Field Cost Breakdown -->
                                                    <div id="fieldCostBreakdown" class="mb-2">
                                                        <div class="fw-semibold mb-1">Field Cost:</div>
                                                        <div id="dayNightBreakdown" class="fs-12 text-muted mb-1">
                                                            <!-- Day/Night breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Field Total: XCG <span id="fieldCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Utility Cost Breakdown -->
                                                    <div id="utilityCostBreakdown" class="mb-2 d-none">
                                                        <div class="fw-semibold mb-1">Utility Costs:</div>
                                                        <div id="utilityDetails" class="fs-12 text-muted mb-1">
                                                            <!-- Utility breakdown will be populated by JavaScript -->
                                                        </div>
                                                        <div class="fs-12">
                                                            <strong>Utility Total: XCG <span id="utilityCost">0.00</span></strong>
                                                        </div>
                                                    </div>

                                                    <!-- Total Cost -->
                                                    <div class="border-top pt-2">
                                                        <div class="d-flex justify-content-between align-items-center">
                                                            <span class="fw-bold">Total Cost:</span>
                                                            <span class="h5 mb-0 text-success">XCG <span id="totalCost">0.00</span></span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column: Customer Information -->
                            <div class="col-xl-6">
                                <div class="card custom-card shadow-none border">
                                    <div class="card-header">
                                        <div class="card-title">Customer Information</div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row gy-3">
                                            <!-- Customer Name -->
                                            <div class="col-xl-12">
                                                <label for="customer_name" class="form-label">Customer Name <span
                                                        class="text-danger">*</span></label>
                                                <input type="text" name="customer_name" id="customer_name" required
                                                    value="{{ old('customer_name', $reservation->customer_name) }}"
                                                    placeholder="Name (required)"
                                                    class="form-control @error('customer_name') is-invalid @enderror">
                                                @error('customer_name')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Email -->
                                            <div class="col-xl-12">
                                                <label for="customer_email" class="form-label">Customer Email</label>
                                                <input type="email" name="customer_email" id="customer_email"
                                                    value="{{ old('customer_email', $reservation->customer_email) }}"
                                                    placeholder="Email (optional)"
                                                    class="form-control @error('customer_email') is-invalid @enderror">
                                                @error('customer_email')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Customer Phone -->
                                            <div class="col-xl-12">
                                                <label for="customer_phone" class="form-label">Customer Phone <span
                                                        class="text-danger">*</span></label>
                                                <input type="tel" name="customer_phone" id="customer_phone" required
                                                    value="{{ old('customer_phone', $reservation->customer_phone) }}"
                                                    placeholder="Phone number (required)"
                                                    class="form-control @error('customer_phone') is-invalid @enderror">
                                                @error('customer_phone')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Special Requests -->
                                            <div class="col-xl-12">
                                                <label for="special_requests" class="form-label">Special Requests</label>
                                                <textarea name="special_requests" id="special_requests" rows="3"
                                                    class="form-control @error('special_requests') is-invalid @enderror"
                                                    placeholder="Any special requirements or notes...">{{ old('special_requests', $reservation->special_requests) }}</textarea>
                                                @error('special_requests')
                                                    <div class="invalid-feedback">{{ $message }}</div>
                                                @enderror
                                            </div>

                                            <!-- Current Reservation Info -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-warning">
                                                    <h6 class="fw-semibold">Current Reservation</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li><strong>Original Date:</strong>
                                                            {{ $reservation->booking_date->format('M d, Y') }}</li>
                                                        <li><strong>Original Time:</strong> {{ $reservation->time_range }}
                                                        </li>
                                                        <li><strong>Original Cost:</strong>
                                                            XCG {{ number_format($reservation->total_cost, 2) }}</li>
                                                        <li><strong>Status:</strong> {{ $reservation->status }}</li>
                                                    </ul>
                                                </div>
                                            </div>

                                            <!-- Modification Rules -->
                                            <div class="col-xl-12">
                                                <div class="alert alert-info">
                                                    <h6 class="fw-semibold">Modification Rules</h6>
                                                    <ul class="mb-0 fs-12">
                                                        <li> Modifications must be made at least 24 hours in advance</li>
                                                        <li> New time slot must be available</li>
                                                        <li> Cost will be recalculated based on new selection</li>
                                                        <li> Reservation will remain confirmed after modification</li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row">
                            <div class="col-xl-12">
                                <div class="d-flex gap-2 justify-content-start mt-4 pt-3 border-top">
                                    <button type="submit" id="submitBtn" class="btn btn-warning">
                                        <i class="ti ti-check me-1"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
    <script>


        // Global utilities array - moved here to be accessible by all functions
        let utilities = [];


        // Preserve and restore reservation field state to avoid side-effects when adding/removing utilities
        function getReservationState() {
            return {
                fieldId: document.getElementById('field_id')?.value || '',
                bookingDate: document.getElementById('booking_date')?.value || '',
                startTime: document.getElementById('start_time')?.value || '',
                endTime: document.getElementById('end_time')?.value || ''
            };
        }

        function restoreReservationState(state) {
            const fieldEl = document.getElementById('field_id');
            const dateEl = document.getElementById('booking_date');
            const startEl = document.getElementById('start_time');
            const endEl = document.getElementById('end_time');

            if (fieldEl && state.fieldId && fieldEl.value !== state.fieldId) {
                fieldEl.value = state.fieldId;
            }
            if (dateEl && state.bookingDate && dateEl.value !== state.bookingDate) {
                dateEl.value = state.bookingDate;
            }
            if (startEl && state.startTime && startEl.value !== state.startTime) {
                // Use normalized time format for comparison
                const normalizedStartTime = normalizeTimeFormat(state.startTime);
                const startOption = Array.from(startEl.options).find(option =>
                    normalizeTimeFormat(option.value) === normalizedStartTime
                );
                if (startOption) {
                    startEl.value = startOption.value;
                }
            }
            if (endEl && state.endTime && endEl.value !== state.endTime) {
                // Use normalized time format for comparison
                const normalizedEndTime = normalizeTimeFormat(state.endTime);
                const endOption = Array.from(endEl.options).find(option =>
                    normalizeTimeFormat(option.value) === normalizedEndTime
                );
                if (endOption) {
                    endEl.value = endOption.value;
                }
            }
        }

        // Helper function to normalize time format from HH:MM:SS to HH:MM
        function normalizeTimeFormat(timeString) {
            if (!timeString) return timeString;
            // If time is in HH:MM:SS format, convert to HH:MM
            if (timeString.length > 5 && timeString.includes(':')) {
                const normalized = timeString.substring(0, 5); // "18:00:00" -> "18:00"
                console.log('Time format normalized:', timeString, '->', normalized);
                return normalized;
            }
            return timeString;
        }

        // Existing reservation data for initialization
        const existingReservationData = {
            field_id: '{{ $reservation->field_id }}',
            booking_date: '{{ $reservation->booking_date->format('Y-m-d') }}',
            start_time: normalizeTimeFormat('{{ $reservation->start_time }}'),
            end_time: normalizeTimeFormat('{{ $reservation->end_time }}'),
            duration_hours: '{{ $reservation->duration_hours }}'
        };

        // Existing utilities data - moved here to be accessible by all functions
        const existingUtilities = @json($reservationUtilities);

        function updateFieldInfo() {
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const fieldInfo = document.getElementById('fieldInfo');

            if (selectedOption.value) {
                document.getElementById('fieldCapacity').textContent = selectedOption.dataset.capacity || 'N/A';
                document.getElementById('fieldRate').textContent = parseFloat(selectedOption.dataset.rate || 0).toFixed(2);
                document.getElementById('fieldRate2').textContent = parseFloat(selectedOption.dataset.rate2 || 0).toFixed(2);
                document.getElementById('fieldHours').textContent = (selectedOption.dataset.opening || '08:00') + ' - ' + (
                    selectedOption.dataset.closing || '22:00');
                document.getElementById('fieldDuration').textContent = 'Min ' + (selectedOption.dataset.minHours || '1') + ' - ' + 
                'Max ' + (selectedOption.dataset.maxHours || '8');
                fieldInfo.classList.remove('d-none');
                calculateCost();
            } else {
                fieldInfo.classList.add('d-none');
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
            }
            checkUtilityPrerequisites(); // Check utility prerequisites when field changes
            enableProgressiveFields(); // Enable progressive field activation
        }

        // Progressive Field Activation Function
        function enableProgressiveFields() {
            // Get form elements
            const fieldSelect = document.getElementById('field_id');
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const startTimeHelp = document.getElementById('startTimeHelp');
            const endTimeHelp = document.getElementById('endTimeHelp');

            const fieldSelected = fieldSelect.value;
            const startTimeSelected = startTimeSelect.value;

            // Step 1: Handle start time based on field selection
            if (fieldSelected) {
                startTimeSelect.disabled = false;
                startTimeHelp.innerHTML = '';
            } else {
                startTimeSelect.disabled = true;
                startTimeSelect.innerHTML = '<option value="">Select Start Time</option>';
                startTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a field first';
                // Cascade: also disable end time when field is cleared
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';

                // Apply race condition protection when end time is cleared due to missing field
                console.log('End time cleared due to missing field, applying race condition protection');
                if (costCalculationTimeout) {
                    clearTimeout(costCalculationTimeout);
                    costCalculationTimeout = null;
                }
                if (currentRequest) {
                    currentRequest.abort();
                    currentRequest = null;
                }
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
                return;
            }

            // Step 2: Handle end time based on start time selection
            if (startTimeSelected) {
                endTimeSelect.disabled = false;
                endTimeHelp.innerHTML = '';
                // Note: updateEndTimeOptions() is called only from start time onchange event
                // to avoid clearing user's end time selection during progressive field validation
            } else {
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';

                // Apply race condition protection when end time is cleared due to missing start time
                console.log('End time cleared due to missing start time, applying race condition protection');
                if (costCalculationTimeout) {
                    clearTimeout(costCalculationTimeout);
                    costCalculationTimeout = null;
                }
                if (currentRequest) {
                    currentRequest.abort();
                    currentRequest = null;
                }
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
            }
        }

        // New functions for start/end time logic
        function updateEndTimeOptions() {
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const endTimeHelp = document.getElementById('endTimeHelp');
            const fieldSelect = document.getElementById('field_id');
            const dateInput = document.getElementById('booking_date');

            const startTime = startTimeSelect.value;
            const selectedField = fieldSelect.options[fieldSelect.selectedIndex];
            const date = dateInput.value;

            if (!startTime || !selectedField.value || !date) {
                // Disable end time if no start time selected
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';

                // When start time is cleared, reset dependent elements
                console.log('Start time cleared manually, resetting dependent elements');
                calculateCost();
                checkUtilityPrerequisites();
                enableProgressiveFields();
                return;
            }

            // Store current end time selection to preserve it, or use existing reservation end time for initial load
            // Normalize the time format to handle HH:MM:SS vs HH:MM format mismatch
            const currentEndTime = normalizeTimeFormat(endTimeSelect.value || existingReservationData.end_time);

            // Show loading state
            endTimeSelect.disabled = true;
            endTimeSelect.innerHTML = '<option value="">Loading available times...</option>';
            endTimeHelp.innerHTML = '<i class="ti ti-loader me-1"></i>Checking availability...';

            // Fetch available end times from server (excluding current reservation)
            fetch(`{{ route('reservations.available-end-times') }}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    field_id: selectedField.value,
                    date: date,
                    start_time: startTime,
                    exclude_reservation_id: {{ $reservation->id }}
                })
            })
            .then(response => response.json())
            .then(data => {
                // Enable end time dropdown
                endTimeSelect.disabled = false;
                endTimeHelp.innerHTML = '';

                if (data.success && data.end_times.length > 0) {
                    // Clear and populate end time options
                    endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                    data.end_times.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option.value;
                        optionElement.textContent = option.text;
                        endTimeSelect.appendChild(optionElement);
                    });

                    // Restore previous end time selection if it's still available
                    // Use normalized time format for comparison
                    if (currentEndTime) {
                        const matchingOption = Array.from(endTimeSelect.options).find(option =>
                            normalizeTimeFormat(option.value) === currentEndTime
                        );
                        if (matchingOption) {
                            endTimeSelect.value = matchingOption.value;
                            console.log('Preserved end time selection:', currentEndTime, '-> option value:', matchingOption.value);
                        }
                    }
                } else {
                    // No available end times
                    endTimeSelect.innerHTML = '<option value="">No available end times</option>';
                    endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-warning"></i>No available end times for this start time';
                }

                // After end time options are updated, trigger dependent functions
                // This ensures utilities and cost calculation work with the preserved end time
                calculateCost();
                checkUtilityPrerequisites();
                enableProgressiveFields();
            })
            .catch(error => {
                console.error('Error fetching available end times:', error);
                endTimeSelect.disabled = false;
                endTimeSelect.innerHTML = '<option value="">Error loading times</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-alert-circle me-1 text-danger"></i>Error loading available times';

                // Even on error, trigger dependent functions to update state
                calculateCost();
                checkUtilityPrerequisites();
                enableProgressiveFields();
            });
        }



        function calculateDurationFromTimes(startTime, endTime) {
            if (!startTime || !endTime) return 0;

            const startDate = new Date(`2000-01-01 ${startTime}`);
            const endDate = new Date(`2000-01-01 ${endTime}`);

            if (endDate <= startDate) return 0;

            return (endDate - startDate) / (1000 * 60 * 60); // Duration in hours
        }

        function formatTimeDisplay(timeString) {
            const time = new Date(`2000-01-01 ${timeString}`);
            return time.toLocaleTimeString('en-US', {
                hour: 'numeric',
                minute: '2-digit',
                hour12: true
            });
        }

        function formatDuration(hours) {
            if (hours === 0.5) return '30 min';
            if (hours === 1) return '1 hour';
            if (hours % 1 === 0) return `${hours} hours`;
            return `${hours} hours`;
        }

        // Legacy client-side calculation removed - now using server-side authoritative calculation

        /////////////////////////////////////////////////////////////////////////////////////////////////
        // Client-side cost calculation removed - using server-side authoritative calculation
        // Handle end time changes with race condition protection
        function handleEndTimeChange() {
            const endTimeSelect = document.getElementById('end_time');
            const endTimeValue = endTimeSelect.value;

            // If end time is cleared, apply race condition protection
            if (!endTimeValue) {
                console.log('End time cleared manually, applying race condition protection');

                // Cancel any pending cost calculation requests
                if (costCalculationTimeout) {
                    clearTimeout(costCalculationTimeout);
                    costCalculationTimeout = null;
                }
                if (currentRequest) {
                    currentRequest.abort();
                    currentRequest = null;
                }

                // Explicitly hide cost displays
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
            }

            // Continue with normal end time change handling
            calculateCost();
            checkUtilityPrerequisites();
            enableProgressiveFields();
        }

        // Performance optimization: debounce and request deduplication
        let costCalculationTimeout;
        let lastRequestData = null;
        let currentRequest = null;

        function calculateCost() {
            const fieldSelect = document.getElementById('field_id');
            const startTimeSelect = document.getElementById('start_time');
            const endTimeSelect = document.getElementById('end_time');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const rate = parseFloat(selectedOption.dataset.rate || 0);

            const fieldId = fieldSelect.value;
            const startTime = startTimeSelect.value;
            const endTime = endTimeSelect.value;

            if (!fieldId || !startTime || !endTime) {
                console.log('Cost calculation: Missing required fields', {
                    fieldId: !!fieldId,
                    startTime: !!startTime,
                    endTime: !!endTime
                });
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
                return;
            }

            // Calculate duration from start and end times
            const duration = calculateDurationFromTimes(startTime, endTime);
            if (duration <= 0) {
                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');
                return;
            }

            // Prepare utilities data for server calculation
            const utilitiesData = utilities.map(u => ({
                id: u.id,
                hours: u.quantity
            }));

            // Debug: Log cost calculation request
            console.log('Cost calculation triggered:', {
                fieldId,
                startTime,
                endTime,
                duration,
                utilities: utilitiesData,
                utilitiesCount: utilities.length,
                utilitiesArray: utilities
            });

            // Create request data for comparison
            const requestData = {
                field_id: fieldId,
                start_time: startTime,
                end_time: endTime,
                utilities: utilitiesData
            };

            // Check if request data has changed (avoid redundant requests)
            const requestDataString = JSON.stringify(requestData);
            if (lastRequestData === requestDataString) {
                return; // No change, skip request
            }

            // Cancel previous timeout
            if (costCalculationTimeout) {
                clearTimeout(costCalculationTimeout);
            }

            // Debounce the request (300ms delay)
            costCalculationTimeout = setTimeout(() => {
                // Cancel previous request if still pending
                if (currentRequest) {
                    currentRequest.abort();
                }

                // Store current request data
                lastRequestData = requestDataString;

                // Create AbortController for request cancellation
                const controller = new AbortController();
                currentRequest = controller;

                // Fetch cost calculation from server
                fetch(`{{ route('reservations.cost-estimate') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field_id: fieldId,
                        start_time: startTime,
                        end_time: endTime,
                        utilities: utilitiesData
                    })
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    // Debug: Log server response
                    console.log('Server response received:', {
                        total_cost: data.total_cost,
                        field_cost: data.field_cost,
                        utility_breakdown_count: data.utility_breakdown ? data.utility_breakdown.length : 0,
                        full_response: data
                    });

                    if (data.error) {
                        console.error('Cost calculation error:', data.error);
                        if (data.validation_errors) {
                            console.error('Validation errors:', data.validation_errors);
                        }
                        return;
                    }

                // Update UI with server-calculated detailed breakdown
                const totalCost = parseFloat(data.total_cost || 0);
                document.getElementById('totalCost').textContent = totalCost.toFixed(2);

                // Update field cost breakdown
                const fieldCost = parseFloat(data.field_cost || data.subtotal || data.total_cost || 0);
                document.getElementById('fieldCost').textContent = fieldCost.toFixed(2);

                // Update day/night rate breakdown
                const dayNightBreakdown = document.getElementById('dayNightBreakdown');
                if (data.rate_breakdown && (data.rate_breakdown.day_hours > 0 || data.rate_breakdown.night_hours > 0)) {
                    let breakdownHtml = '';
                    if (data.rate_breakdown.day_hours > 0) {
                        const dayRate = parseFloat(data.hourly_rate || 0);
                        const dayCost = parseFloat(data.rate_breakdown.day_cost || 0);
                        breakdownHtml += `Day Rate: ${data.rate_breakdown.day_hours} hours × XCG ${dayRate.toFixed(2)} = XCG ${dayCost.toFixed(2)}<br>`;
                    }
                    if (data.rate_breakdown.night_hours > 0) {
                        const nightRate = parseFloat(data.night_hourly_rate || 0);
                        const nightCost = parseFloat(data.rate_breakdown.night_cost || 0);
                        breakdownHtml += `Night Rate: ${data.rate_breakdown.night_hours} hours × XCG ${nightRate.toFixed(2)} = XCG ${nightCost.toFixed(2)}`;
                    }
                    dayNightBreakdown.innerHTML = breakdownHtml;
                } else {
                    // Simple rate display for fields without night rates
                    dayNightBreakdown.innerHTML = `${duration} hours × XCG ${rate.toFixed(2)} = XCG ${fieldCost.toFixed(2)}`;
                }

                // Update utility cost breakdown
                const utilityCostBreakdown = document.getElementById('utilityCostBreakdown');
                const utilityDetails = document.getElementById('utilityDetails');
                const utilityCostSpan = document.getElementById('utilityCost');

                console.log('Processing utility breakdown:', {
                    hasUtilityBreakdown: !!data.utility_breakdown,
                    utilityBreakdownLength: data.utility_breakdown ? data.utility_breakdown.length : 0,
                    utilitiesInForm: utilities.length
                });

                if (data.utility_breakdown && data.utility_breakdown.length > 0) {
                    let utilityHtml = '';
                    let totalUtilityCost = 0;

                    data.utility_breakdown.forEach(utility => {
                        const utilityRate = parseFloat(utility.rate || 0);
                        const utilityCost = parseFloat(utility.cost || 0);
                        utilityHtml += `${utility.name}: ${utility.hours} × XCG ${utilityRate.toFixed(2)} = XCG ${utilityCost.toFixed(2)}<br>`;
                        totalUtilityCost += utilityCost;

                        // Update utility costs in table with multiple methods
                        // Method 1: Direct cost cell ID (most reliable)
                        const costCellById = document.getElementById(`utility-cost-${utility.utility_id}`);

                        if (costCellById) {
                            costCellById.innerHTML = `XCG ${utilityCost.toFixed(2)}`;
                            console.log(`✅ Utility cost updated: ${utility.name} = XCG ${utilityCost.toFixed(2)}`);
                            return; // Success, no need for fallback methods
                        }

                        // Method 2: Row data attribute
                        let utilityRow = document.querySelector(`#utilityTable tbody tr[data-utility-id="${utility.utility_id}"]`);

                        // Method 3: Try finding by hidden input name
                        if (!utilityRow) {
                            const rows = document.querySelectorAll('#utilityTable tbody tr');
                            rows.forEach(row => {
                                const hiddenInput = row.querySelector(`input[name="utilities[${utility.utility_id}][id]"]`);
                                if (hiddenInput) {
                                    utilityRow = row;
                                }
                            });
                        }

                        // Method 4: Try finding by utility name (less reliable but better than nothing)
                        if (!utilityRow) {
                            const rows = document.querySelectorAll('#utilityTable tbody tr');
                            rows.forEach(row => {
                                const nameCell = row.cells[0];
                                if (nameCell && nameCell.textContent.trim() === utility.name) {
                                    utilityRow = row;
                                }
                            });
                        }

                        if (utilityRow) {
                            const costCell = utilityRow.cells[3]; // Cost column (0: name, 1: quantity, 2: rate, 3: cost, 4: action)
                            if (costCell) {
                                costCell.innerHTML = `XCG ${utilityCost.toFixed(2)}`;
                                console.log(`✅ Utility cost updated (fallback): ${utility.name} = XCG ${utilityCost.toFixed(2)}`);
                            }
                        } else {
                            console.warn(`⚠️ Could not find utility row for ${utility.name} (ID: ${utility.utility_id})`);
                        }
                    });

                    utilityDetails.innerHTML = utilityHtml;
                    utilityCostSpan.textContent = totalUtilityCost.toFixed(2);
                    utilityCostBreakdown.classList.remove('d-none');
                } else {
                    utilityCostBreakdown.classList.add('d-none');
                    utilityCostSpan.textContent = '0.00';
                }

                // Ensure success styling and show cost overview card
                // But first verify that both start time AND end time are still selected (prevent race conditions)
                const currentStartTime = document.getElementById('start_time').value;
                const currentEndTime = document.getElementById('end_time').value;
                if (currentStartTime && currentEndTime) {
                    const costDisplay = document.getElementById('costDisplay');
                    costDisplay.className = 'alert alert-success d-block';
                    costDisplay.classList.remove('d-none');
                    document.getElementById('costOverviewCard').classList.remove('d-none');
                } else {
                    console.log('Cost calculation response received but start time or end time is now empty, keeping cost displays hidden', {
                        startTime: !!currentStartTime,
                        endTime: !!currentEndTime
                    });
                }
            })
            .catch(error => {
                console.error('Error calculating cost:', error);
                // Show error state instead of client-side calculation
                document.getElementById('totalCost').textContent = 'Error';
                document.getElementById('fieldCost').textContent = 'Error';
                document.getElementById('costOverviewCard').classList.remove('d-none');

                // Show user-friendly error message
                const costDisplay = document.getElementById('costDisplay');
                costDisplay.className = 'alert alert-warning d-block';
                costDisplay.classList.remove('d-none');
                costDisplay.innerHTML = `
                    <h6 class="fw-semibold">Cost Calculation Unavailable</h6>
                    <p class="mb-0">Unable to calculate cost at this time. Please try again or contact support.</p>
                `;
            });
            }, 300); // Close setTimeout
        }
        /////////////////////////////////////////////////////////////////////////////////////////////////

        function loadAvailability() {
            const fieldId = document.getElementById('field_id').value;
            const date = document.getElementById('booking_date').value;

            if (!fieldId || !date) {
                return;
            }

            // Get field constraints for minimum duration
            const fieldSelect = document.getElementById('field_id');
            const selectedOption = fieldSelect.options[fieldSelect.selectedIndex];
            const minDuration = parseFloat(selectedOption.dataset.minHours || 0.5);

            console.log('Loading availability for editing with min duration:', minDuration);

            // Load available start time slots (using minimum duration for initial availability)
            fetch(`{{ route('reservations.check-availability') }}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        field_id: fieldId,
                        date: date,
                        duration_hours: minDuration,
                        exclude_reservation_id: {{ $reservation->id }}
                    })
                })
                .then(response => response.json())
                .then(data => {
                    console.log('Received availability data:', data);
                    updateTimeSlots(data.slots || []);
                })
                .catch(error => {
                    console.error('Error loading availability:', error);
                });
        }

        function updateTimeSlots(slots) {
            const timeSelect = document.getElementById('start_time');
            const currentValue = timeSelect.value;

            // For initial load, try to use existing reservation start time if no current value
            // Normalize the target value to handle HH:MM:SS vs HH:MM format mismatch
            const targetValue = normalizeTimeFormat(currentValue || existingReservationData.start_time);

            // Clear existing options
            timeSelect.innerHTML = '<option value="">Select Start Time</option>';

            // Add available slots only
            let timeSelected = false;
            slots.forEach(slot => {
                const option = document.createElement('option');
                option.value = slot.start_time;
                // Show only the time value for start time (e.g., "13:00")
                option.textContent = slot.start_time;
                // Select if this matches our target value and it's available
                // Normalize both values for comparison to handle HH:MM:SS vs HH:MM format mismatch
                if (normalizeTimeFormat(slot.start_time) === targetValue) {
                    option.selected = true;
                    timeSelected = true;
                }
                timeSelect.appendChild(option);
            });

            // Hide any previous availability warnings
            document.getElementById('availabilityCheck').classList.add('d-none');

            // Check if start time is actually selected after populating slots
            // This handles cases where previous start time becomes invalid for new field
            const startTimeSelect = document.getElementById('start_time');
            const actualStartValue = startTimeSelect.value;

            if (actualStartValue) {
                // Start time is valid for new field, update end time options
                // updateEndTimeOptions() will handle calling dependent functions after completion
                console.log('Start time preserved for new field:', actualStartValue);
                updateEndTimeOptions();
            } else {
                // Start time was cleared (invalid for new field) or never selected
                // Reset all dependent elements and run validation
                console.log('Start time cleared due to field change, resetting dependent elements');

                // Reset end time dropdown
                const endTimeSelect = document.getElementById('end_time');
                const endTimeHelp = document.getElementById('endTimeHelp');
                endTimeSelect.disabled = true;
                endTimeSelect.innerHTML = '<option value="">Select End Time</option>';
                endTimeHelp.innerHTML = '<i class="ti ti-info-circle me-1"></i>Please select a start time first';

                // Explicitly hide cost displays when start time is cleared
                console.log('Explicitly hiding cost displays due to cleared start time');

                // Cancel any pending cost calculation requests to prevent them from re-showing cost displays
                if (costCalculationTimeout) {
                    clearTimeout(costCalculationTimeout);
                    costCalculationTimeout = null;
                }
                if (currentRequest) {
                    currentRequest.abort();
                    currentRequest = null;
                }

                document.getElementById('costDisplay').classList.add('d-none');
                document.getElementById('costOverviewCard').classList.add('d-none');

                // Run dependent functions to reset utilities and cost display
                console.log('Calling dependent functions with cleared start time:', {
                    startTimeValue: startTimeSelect.value,
                    endTimeValue: endTimeSelect.value
                });
                checkUtilityPrerequisites();
                calculateCost();
                enableProgressiveFields();
            }
        }

        // Progressive Form Flow Functions
        function checkUtilityPrerequisites() {
            const fieldId = document.getElementById('field_id').value;
            const bookingDate = document.getElementById('booking_date').value;
            const startTime = document.getElementById('start_time').value;
            const endTime = document.getElementById('end_time').value;

            // Check if all required fields are selected (strict dependency chain)
            const allRequiredFieldsSelected = fieldId && bookingDate && startTime && endTime;

            console.log('Utility prerequisites check:', {
                fieldId: !!fieldId,
                bookingDate: !!bookingDate,
                startTime: !!startTime,
                endTime: !!endTime,
                allRequiredFieldsSelected
            });

            const utilitySelect = document.getElementById('utilitySelect');
            const utilityQuantity = document.getElementById('utilityQuantity');
            const addUtilityBtn = document.getElementById('addUtilityBtn');
            const utilitySection = document.getElementById('utilitySelectionSection');
            const utilityTableContainer = document.getElementById('utilityTableContainer');
            const prerequisitesMessage = document.getElementById('utilityPrerequisitesMessage');

            if (allRequiredFieldsSelected) {
                // Enable utilities section
                utilitySelect.disabled = false;
                utilityQuantity.disabled = false;
                addUtilityBtn.disabled = false;
                utilitySection.className = 'utility-section-enabled';
                utilityTableContainer.className = 'utility-table-enabled';
                prerequisitesMessage.classList.add('d-none');

                // Always show utility table container when utilities are enabled
                utilityTableContainer.style.display = '';

                // Enable all existing utility action buttons
                enableUtilityTableInteractions(true);

                // Add visual indicators to completed fields
                addFormStepIndicator('field_id');
                addFormStepIndicator('booking_date');
                addFormStepIndicator('start_time');
                addFormStepIndicator('end_time');
            } else {
                // Disable utilities section
                utilitySelect.disabled = true;
                utilityQuantity.disabled = true;
                addUtilityBtn.disabled = true;
                utilitySection.className = 'utility-section-disabled';
                prerequisitesMessage.classList.remove('d-none');

                // Handle utility table visibility based on whether utilities exist
                if (utilities.length === 0) {
                    // No utilities exist - hide table container completely
                    utilityTableContainer.style.display = 'none';
                    utilityTableContainer.className = 'utility-table-disabled';
                } else {
                    // Utilities exist - show table but apply disabled styling
                    utilityTableContainer.style.display = '';
                    utilityTableContainer.className = 'utility-table-disabled';
                }

                // Disable all existing utility action buttons
                enableUtilityTableInteractions(false);

                // Update prerequisites message with specific missing fields
                updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime);

                // Remove visual indicators from incomplete fields
                removeFormStepIndicator('field_id');
                removeFormStepIndicator('booking_date');
                removeFormStepIndicator('start_time');
                removeFormStepIndicator('end_time');
            }
        }

        function updatePrerequisitesMessage(fieldId, bookingDate, startTime, endTime) {
            const missingFields = [];
            if (!fieldId) missingFields.push('field');
            if (!bookingDate) missingFields.push('date');
            if (!startTime) missingFields.push('start time');
            if (!endTime) missingFields.push('end time');

            const message = document.getElementById('utilityPrerequisitesMessage');
            if (missingFields.length > 0) {
                const fieldList = missingFields.join(', ');
                message.innerHTML = `
                    <p class="mb-0"><i class="ti ti-info-circle me-2"></i><strong>Complete reservation details first</strong></p>
                    <p class="mb-0" style="margin-left: 1.3rem;">Please select ${fieldList} before adding utilities.</p>
                `;
            }
        }

        function addFormStepIndicator(fieldId) {
            const field = document.getElementById(fieldId);
            if (field && field.value) {
                field.classList.add('form-step-complete');
            }
        }

        function removeFormStepIndicator(fieldId) {
            const field = document.getElementById(fieldId);
            if (field) {
                field.classList.remove('form-step-complete');
            }
        }

        function enableUtilityTableInteractions(enabled) {
            // Find all remove buttons in the utility table
            const removeButtons = document.querySelectorAll('#utilityTable .btn-danger');

            removeButtons.forEach(button => {
                if (enabled) {
                    button.disabled = false;
                    button.classList.remove('disabled');
                    button.style.pointerEvents = 'auto';
                } else {
                    button.disabled = true;
                    button.classList.add('disabled');
                    button.style.pointerEvents = 'none';
                }
            });

            // Also handle any quantity inputs in the table if they exist
            const quantityInputs = document.querySelectorAll('#utilityTable input[type="number"]');
            quantityInputs.forEach(input => {
                input.disabled = !enabled;
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing edit form with existing reservation data:', existingReservationData);

            // First, update field info to populate field-dependent data
            updateFieldInfo();

            // Load existing utilities first (before other initialization)
            console.log('Loading existing utilities:', existingUtilities);
            if (Array.isArray(existingUtilities)) {
                existingUtilities.forEach(util => {
                    console.log('Adding existing utility:', util);
                    addUtility(util.id, util.name, parseFloat(util.rate), parseInt(util.hours), false);
                });
                console.log('Utilities array after loading existing:', utilities);
            } else {
                console.log('No existing utilities to load or existingUtilities is not an array');
            }

            // Load availability to populate time slots with existing start time
            loadAvailability();

            // Set up form state after availability loads
            setTimeout(function() {
                console.log('Setting up form state after availability load...');

                // Check if start time is available and selected
                const startTimeSelect = document.getElementById('start_time');
                console.log('Start time after availability load:', startTimeSelect.value);

                // If start time is available, proceed with end time setup
                if (startTimeSelect.value) {
                    updateEndTimeOptions();

                    // Set end time after a short delay to ensure options are loaded
                    setTimeout(function() {
                        const endTimeSelect = document.getElementById('end_time');

                        // If end time is not yet set but we have existing data, try to set it
                        if (!endTimeSelect.value && existingReservationData.end_time) {
                            // Use normalized time format for comparison
                            const normalizedEndTime = normalizeTimeFormat(existingReservationData.end_time);
                            const endOption = Array.from(endTimeSelect.options).find(option =>
                                normalizeTimeFormat(option.value) === normalizedEndTime
                            );
                            if (endOption) {
                                endTimeSelect.value = endOption.value;
                                console.log('Set end time to existing value:', existingReservationData.end_time, '-> option value:', endOption.value);
                            } else {
                                console.log('Existing end time not available in current slots:', existingReservationData.end_time);
                            }
                        }

                        // Final initialization after all times are set
                        enableProgressiveFields();
                        checkUtilityPrerequisites();
                        calculateCost();

                        console.log('Edit form initialization complete');
                    }, 300);
                } else {
                    // If no start time available, log why and run basic initialization
                    console.log('Start time not available in current slots:', existingReservationData.start_time);
                    enableProgressiveFields();
                    checkUtilityPrerequisites();
                }
            }, 800);
        });
    </script>


    <script>
        /////////////////////////////////// di utilities
        // utilities array is now declared in the first script tag

        function addUtility(id = null, name = '', rate = 0, quantity = 1, triggerCalculation = true) {
            // Preserve current reservation state to avoid side effects
            const prevState = getReservationState();

            const tableBody = document.querySelector('#utilityTable tbody');
            const tableHeader = document.getElementById('utilityTableHeader');

            if (!id) {
                const select = document.getElementById('utilitySelect');
                const quantityInput = document.getElementById('utilityQuantity');

                id = select.value;
                name = select.options[select.selectedIndex].dataset.name;
                rate = parseFloat(select.options[select.selectedIndex].dataset.rate || 0);
                quantity = parseInt(quantityInput.value || 1);

                if (!id || quantity <= 0 || !Number.isInteger(quantity)) {
                    showInfoModal('utilityValidationModal');
                    return;
                }

                if (utilities.find(u => u.id === id)) {
                    showInfoModal('utilityDuplicateModal');
                    return;
                }

                // Reset form inputs only when adding from UI
                select.selectedIndex = 0;
                quantityInput.value = 1;
            }

            if (utilities.find(u => u.id === id)) return;

            utilities.push({
                id,
                name,
                rate,
                quantity
            });

            const row = document.createElement('tr');
            row.setAttribute('data-utility-id', id);
            row.id = `utility-row-${id}`; // Add unique ID for more reliable selection
            row.innerHTML = `
        <td>${name}</td>
        <td class="utility-hours">${quantity}</td>
        <td class="utility-rate">${rate.toFixed(2)}</td>
        <td id="utility-cost-${id}"><span class="text-muted">Calculating...</span></td>
        <td>
            <button type="button" class="btn btn-danger btn-sm" onclick="removeUtility('${id}', this)">Remove</button>
        </td>
        <input type="hidden" name="utilities[${id}][id]" value="${id}">
        <input type="hidden" name="utilities[${id}][hours]" value="${quantity}">
    `;

            tableBody.appendChild(row);

            // Show table header when first utility is added
            if (utilities.length === 1) {
                tableHeader.classList.remove('d-none');
            }

            // Ensure reservation fields stay intact
            restoreReservationState(prevState);

            // Update utility table visibility based on current state
            checkUtilityPrerequisites();

            // Ensure the table maintains the correct disabled state
            const utilityTableContainer = document.getElementById('utilityTableContainer');
            if (utilityTableContainer.classList.contains('utility-table-disabled')) {
                // If table is disabled, ensure new buttons are also disabled
                enableUtilityTableInteractions(false);
            }

            // Only trigger cost calculation if requested (avoid multiple calls during initial load)
            if (triggerCalculation) {
                calculateCost();
            }
        }



        function removeUtility(id, btn) {
            // Preserve current reservation state to avoid side effects
            const prevState = getReservationState();

            utilities = utilities.filter(u => u.id !== id);
            btn.closest('tr').remove();

            // Hide table header when no utilities remain
            const tableHeader = document.getElementById('utilityTableHeader');

            if (utilities.length === 0) {
                tableHeader.classList.add('d-none');
            }

            // Ensure reservation fields stay intact
            restoreReservationState(prevState);

            // Update utility table visibility based on current state
            checkUtilityPrerequisites();

            // Recalculate cost and keep display visible
            calculateCost();
        }

        // Function to show info modals
        function showInfoModal(modalId) {
            const modal = new bootstrap.Modal(document.getElementById(modalId));
            modal.show();
        }
    </script>

    <!-- existingUtilities is now declared in the first script tag -->
@endpush

<!-- Info Modals for Utility Validation -->
<x-confirmation-modal
    modal-id="utilityValidationModal"
    type="info"
    modal-title="Validation Error"
    title="Please select a utility and enter a valid whole number quantity."
    warning-text=""
    dismiss-text="OK"
    form-action="#"
/>

<x-confirmation-modal
    modal-id="utilityDuplicateModal"
    type="info"
    modal-title="Duplicate Utility"
    title="This utility is already added."
    warning-text=""
    dismiss-text="OK"
    form-action="#"
/>
